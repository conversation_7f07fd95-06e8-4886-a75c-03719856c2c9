<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import sessionStore from '@/store/modules/session.store';
import { WechatService } from '@/service/wechat';
import { OauthService } from '@/service/oauth';

@Component
export default class Oauth extends Vue {
  loading: boolean = true;
  code: string = '';
  paramsvalue: string = '';
  openid: string = '';
  debug: boolean = process.env.VUE_APP_WECHAT_AUTH === 'false';

  mounted() {
    this.paramsvalue = this.$route.query.paramsvalue as string;
    const { internal } = this.$route.query;

    // 如果是内部重定向，直接进入目标页面
    if (internal === 'redirect') {
      this.enter();
      return;
    }

    // 根据是否有 paramsvalue 参数选择认证方式
    if (this.paramsvalue) {
      this.oauthAuthorize();
    } else {
      this.wechatAuthorize();
    }
  }

  async oauthWithToken() {
    try {
      // 获取openid
      const {
        data: { token },
      } = await OauthService.fetchToken();
      const {
        data: { openid },
      } = await WechatService.getOpenid(this.code);
      this.openid = openid;

      window.localStorage.setItem('openid', openid);
      const redirectUri = `http://wchattest.stiei.edu.cn/campus-mobile/oauth`;
      const url = 'http://wechatoauth.stiei.edu.cn/oauth/authopenid';
      const href = `${url}?token=${token}&openid=${openid}&redirectUri=${redirectUri}`;
      window.location.href = href;
    } catch (error) {
      this.goToLogin();
    }
  }

  // 1、Development Env
  async debugAuthorize() {
    try {
      await sessionStore.check();
      this.enter();
    } catch (error) {
      this.goToLogin();
    }
  }

  oauthAuthorize() {
    this.oauthSignIn(this.paramsvalue);
  }

  async oauthSignIn(paramsvalue: string) {
    try {
      const openid = window.localStorage.getItem('openid') || this.openid;
      const {
        data: { account },
      } = await OauthService.fetchAccount(paramsvalue, openid);

      // 检查是否需要重新登录
      const needReLogin = !sessionStore.token || !sessionStore.currentUser || sessionStore.currentUser.code !== account;

      if (needReLogin) {
        await sessionStore.oauthSignIn(paramsvalue, openid, account);
      } else {
        // 如果已经是正确的用户，只需要验证 token 有效性
        try {
          await sessionStore.check();
        } catch (error) {
          // token 无效，重新登录
          await sessionStore.oauthSignIn(paramsvalue, openid, account);
        }
      }

      // 绑定微信号
      WechatService.bindWechat(openid);
      this.enter();
    } catch (error) {
      this.goToLogin();
    }
  }

  // 2、Production Env
  wechatAuthorize() {
    this.code = this.$route.query.code as string;
    if (this.code) {
      this.validateToken();
    } else {
      WechatService.auth('oauth');
    }
  }

  validateToken() {
    // if (sessionStore.token) {
    //   this.checkToken();
    // } else {
    //   this.oauthWithToken();
    //   // this.signInWithWechatCode();
    // }
    this.oauthWithToken();
  }

  // 强制清除token
  async clearToken() {
    if (sessionStore.token) {
      const redirectPath = this.$utils.getRedirectPath();
      await sessionStore.signOut();
      window.localStorage.clear();
      this.$utils.setRedirectPath(redirectPath);
    }
  }

  async checkToken() {
    try {
      await sessionStore.check();
      this.bindWechat();
    } catch (error) {
      this.oauthWithToken();
      // this.signInWithWechatCode();
    }
  }

  async bindWechat() {
    try {
      await WechatService.bind(this.code);
      this.enter();
    } catch (error) {
      this.$message.warning('绑定微信失败');
      this.enter();
    }
  }

  async signInWithWechatCode() {
    try {
      await sessionStore.wechatSignIn(this.code);
      this.enter();
    } catch (error) {
      this.oauthWithToken();
      // this.goToLogin();
    }
  }

  enter() {
    this.loading = false;
    // 使用 redirectUrl 查询参数，如果不存在则使用保存的 redirectPath
    const redirectTo = this.$utils.getRedirectPath() || '/portal/user/todo';

    // 防止重定向到 oauth 相关页面造成循环
    if (redirectTo.includes('oauth') || redirectTo.includes('login')) {
      this.$router.replace('/portal/user/todo').catch(() => {});
      return;
    }

    // 确保添加 internal 参数防止重复认证
    const targetQuery: any = { internal: 'redirect' };

    // 保留原有的查询参数，但 internal 参数优先
    if (this.$route.query) {
      Object.keys(this.$route.query).forEach(key => {
        if (key !== 'internal' && key !== 'code' && key !== 'paramsvalue') {
          targetQuery[key] = this.$route.query[key];
        }
      });
    }

    this.$router
      .replace({
        path: redirectTo,
        query: targetQuery,
      })
      .catch(() => {});
  }

  goToLogin() {
    this.loading = false;
    this.$router.replace('/login').catch(() => {});
  }
}
</script>

<template lang="pug">
.container(v-loading='loading')
</template>

<style lang="stylus" scoped>
.container
  position fixed
  top 0
  right 0
  bottom 0
  left 0
</style>
